import React, { useEffect, useRef, useState } from "react";
import { Dialog } from "primereact/dialog";
import { <PERSON><PERSON> } from "primereact/button";
import { useSelector } from "react-redux";
import moment from "moment";
import ConsolidateDialog from "./ConsolidateDialog";
import { API } from "../constants/api_url";

const statusColors = {
    Completed: "#29C76F",
    "In Progress": "#F5C37B",
    "Not Started": "#CCCED5",
    Blocked: "#fff",
};

const QualitativeResponse = ({ data, categoryData, refresh }) => {
    console.log(data)
    const login_data = useSelector((state) => state.user.userdetail);
    const [visible, setVisible] = useState(false);
    const [selectedItem, setSelectedItem] = useState(null);
    const [formData, setFormData] = useState({});
    const userList = useSelector(state => state.userlist.userList);

    // State for consolidate dialog
    const [consolidateDialogVisible, setConsolidateDialogVisible] = useState(false);
    const [selectedConsolidateSection, setSelectedConsolidateSection] = useState({
        assignmentId: null,
        sectionId: null
    });

    const didMount = useRef(false);

    useEffect(() => {
        if (didMount.current) {
            if (!consolidateDialogVisible) {
                refresh();
            }
        } else {
            didMount.current = true;
        }
    }, [consolidateDialogVisible]);
    const getUser = (id) => {
        let user_name = 'Not Found'
        let index = userList.findIndex(i => i.id === Number(id))
        if (index !== -1) {
            user_name = userList[index].information.empname
        }
        return user_name
    }
    const openDialog = (item) => {
        const prefilled = {};
        item.form?.forEach((field) => {
            if (field.type === "checkbox-group") {
                prefilled[field.name] = Array.isArray(field.value) ? field.value : [];
            } else {
                prefilled[field.name] = field.value || "";
            }
        });

        setSelectedItem(item);
        setFormData(prefilled);
        setVisible(true);
    };

    return (
        <div className="p-4 border rounded-lg shadow-sm bg-white mb-4">
            <h2 className="fs-4 fw-bold mb-4">{data[0]?.title || "Environment"}</h2>


            <div style={{ overflowX: "auto", maxWidth: "100%" }}>
                <table className="table table-bordered" style={{ minWidth: "1000px", width: "100%" }}>
                    <tbody>
                        {data.map((env, index) => {
                            const allSectionNames = new Set();
                            Object.values(env.data).forEach((userSections) => {
                                Object.keys(userSections).forEach((section) => allSectionNames.add(section));
                            });

                            const isFirstOfGroup = index === 0 || data[index - 1].subHeading !== env.subHeading;

                            const subHeadingRow = isFirstOfGroup ? (
                                <tr key={`subheading-${env.subHeading}`}>
                                    <td colSpan={2} className="bg-light fw-bold px-4 py-3 border" style={{ position: "sticky", top: 0, zIndex: 5 }}>
                                        <h5 className="mb-0">{env.subHeading}</h5>
                                    </td>
                                </tr>
                            ) : null;

                            const rows = Object.entries(env.data).map(([userId, sections], userIndex) => userId !== 'Consolidate' ? (

                                <tr key={`${index}-${userId}`}>
                                    <td
                                        className="p-4 align-top bg-white"
                                        style={{ width: "300px", minWidth: "250px", position: "sticky", left: 0, zIndex: 2 }}
                                    >
                                        <p className="fw-semibold fs-5">Reporter: {getUser(userId)}</p>
                                        <p className="text-muted">{env.location}</p>
                                    </td>
                                    <td className="p-0">
                                        <div style={{ display: "flex", overflowX: "auto", whiteSpace: "nowrap", maxWidth: "100%" }}>
                                            {(() => {
                                                // Find all section names from the topic structure in categoryData
                                                const currentTopic = env.subHeading;
                                                const currentCategory = env.title;

                                                // Find the matching category and topic in categoryData
                                                const matchedCategory = categoryData?.find(cat => cat.name === currentCategory);
                                                const matchedTopic = matchedCategory?.qTopics?.find(topic => topic.name === currentTopic);

                                                // Get all section names from the topic structure
                                                const topicSectionNames = matchedTopic?.qSections?.map(section => section.name) || [];

                                                // Combine section names from both data and allSectionNames
                                                const combinedSectionNames = new Set([...allSectionNames, ...topicSectionNames]);

                                                return [...combinedSectionNames].map((sectionName, i) => {
                                                    const item = sections[sectionName] || {
                                                        name: sectionName,
                                                        status: "Blocked",
                                                        dueDate: "",
                                                        isLocked: false,
                                                        form: [],
                                                        sectionId: matchedTopic?.qSections?.find(s => s.name === sectionName)?.id
                                                    };
                                                    const isBlocked = item.status === "Blocked";

                                                    return (
                                                        <div
                                                            key={i}
                                                            className={`d-flex flex-column border flex-shrink-0 ${isBlocked ? "text-muted" : "hover-effect"}`}
                                                            style={{
                                                                width: "250px",
                                                                minWidth: "250px",
                                                                cursor: isBlocked ? "not-allowed" : "pointer",
                                                                opacity: isBlocked ? 0.8 : 1,
                                                                pointerEvents: isBlocked ? "none" : "auto",
                                                            }}
                                                            onClick={() => !isBlocked && openDialog(item)}
                                                        >
                                                            <div
                                                                className="w-100"
                                                                style={{ height: "10px", backgroundColor: statusColors[item.status] || "#fff" }}
                                                            />
                                                            <div className="p-3 flex-grow-1">
                                                                <h5 className="fw-bold text-wrap">{item.name}</h5>
                                                                {!isBlocked && (
                                                                    <p className="text-muted mt-2">Due date:<br /> {item.dueDate}</p>
                                                                )}
                                                            </div>
                                                        </div>
                                                    );
                                                });
                                            })()}
                                        </div>
                                    </td>
                                </tr>
                            ) : null).filter(x => x)

                            const sectionMap = {};
                            Object.entries(env.data).forEach(([userId, sections]) => {
                                Object.entries(sections).forEach(([sectionName, sectionData]) => {

                                    if (!sectionMap[sectionName]) sectionMap[sectionName] = [];
                                    sectionMap[sectionName].push({
                                        ...sectionData,
                                        userId,
                                        location: env.location,
                                        rawResponse: sectionData.form?.reduce((acc, field) => {
                                            if (field.type === "checkbox-group") {
                                                acc[field.name] = Array.isArray(field.value) ? field.value : [];
                                            } else {
                                                acc[field.name] = field.value || "";
                                            }
                                            return acc;
                                        }, {}),
                                        form: sectionData.form || [],
                                    });
                                });
                            });

                            const consolidatedRowKey = `${env.subHeading}`;
                            const isLastOfGroup = index === data.length - 1 || data[index + 1].subHeading !== env.subHeading;

                            // Determine overall consolidated status for the entire row
                            let overallConsolidatedStatus = "Not Started";
                            const allResponses = [];

                            // Collect all individual responses across all sections
                            Object.values(sectionMap).forEach(responses => {
                                responses.filter(x => x.userId !== "Consolidate").forEach(resp => {
                                    allResponses.push(resp);
                                });
                            });

                            if (allResponses.length > 0) {
                                // Check if all responses are Completed (green)
                                const allCompleted = allResponses.every(resp => resp.status === "Completed");

                                // Check if any response is In Progress (yellow)
                                const anyInProgress = allResponses.some(resp => resp.status === "In Progress");

                                if (allCompleted) {
                                    overallConsolidatedStatus = "Completed";
                                } else if (anyInProgress) {
                                    overallConsolidatedStatus = "In Progress";
                                }
                            }

                            // Set background color based on status
                            const consolidatedRowBgColor =
                                overallConsolidatedStatus === "Completed" ? "#e6f7ef" :
                                    overallConsolidatedStatus === "In Progress" ? "#fff8e6" : "#f5f5f5";

                            const consolidatedRow = isLastOfGroup ? (
                                <tr key={`consolidated-${consolidatedRowKey}`}>
                                    <td
                                        className="p-4 align-top fw-bold"
                                        style={{
                                            width: "300px",
                                            minWidth: "250px",
                                            position: "sticky",
                                            left: 0,
                                            zIndex: 2,
                                            backgroundColor: consolidatedRowBgColor
                                        }}
                                    >
                                        <h3 className="fw-semibold fs-5">Consolidated Report</h3>
                                        <p className="text-muted">Across all users</p>
                                    </td>
                                    <td className="p-0" style={{ backgroundColor: consolidatedRowBgColor }}>
                                        <div style={{ display: "flex", overflowX: "auto", whiteSpace: "nowrap", maxWidth: "100%" }}>
                                            {(() => {
                                                // Find all section names from the topic structure in categoryData
                                                const currentTopic = env.subHeading;
                                                const currentCategory = env.title;

                                                // Find the matching category and topic in categoryData
                                                const matchedCategory = categoryData?.find(cat => cat.name === currentCategory);
                                                const matchedTopic = matchedCategory?.qTopics?.find(topic => topic.name === currentTopic);

                                                // Get all section names from the topic structure
                                                const topicSectionNames = matchedTopic?.qSections?.map(section => section.name) || [];

                                                // Combine section names from both data and topic structure
                                                const combinedSectionNames = new Set([...Object.keys(sectionMap), ...topicSectionNames]);

                                                return [...combinedSectionNames].map((sectionName, i) => {
                                                    // Get responses for this section if they exist
                                                    const responses = sectionMap[sectionName] || [];

                                                    // Find the consolidate response
                                                    const consolidateResponse = responses.find(x => x?.userId === "Consolidate") || {};
console.log("consolidateResponse", consolidateResponse,responses);
                                                    // Get section ID from the topic structure
                                                    const sectionId = matchedTopic?.qSections?.find(s => s.name === sectionName)?.id;

                                                    // If no consolidate response exists, create a placeholder
                                                    if (!consolidateResponse.userId) {
                                                        consolidateResponse.status = "Blocked";
                                                        consolidateResponse.name = sectionName;
                                                        consolidateResponse.sectionId = sectionId;
                                                    }

                                                    // Determine consolidated status based on individual responses
                                                    let consolidatedStatus = "Not Started";

                                                    // Filter out the Consolidate user to only check individual responses
                                                    const individualResponses = responses.filter(x => x?.userId !== "Consolidate");

                                                    if (individualResponses.length > 0) {
                                                        // Check if all responses are Completed (green)
                                                        const allCompleted = individualResponses.every(resp => resp.status === "Completed");

                                                        // Check if any response is In Progress (yellow)
                                                        const anyInProgress = individualResponses.some(resp => resp.status === "In Progress");

                                                        if (allCompleted) {
                                                            consolidatedStatus = "Completed";
                                                        } else if (anyInProgress) {
                                                            consolidatedStatus = "In Progress";
                                                        }
                                                    }

                                                    return (
                                                        <div
                                                            key={i}
                                                            className={`d-flex flex-column border flex-shrink-0 ${!consolidateResponse.assignmentId ? "text-muted" : "hover-effect"}`}
                                                            style={{
                                                                width: "250px",
                                                                minWidth: "250px",
                                                                cursor: consolidateResponse.assignmentId ? "pointer" : 'default',
                                                                opacity: !consolidateResponse.assignmentId ? 0.8 : 1,
                                                                backgroundColor: consolidateResponse.assignmentId ?
                                                                    (consolidatedStatus === "Completed" ? "#e6f7ef" :
                                                                        consolidatedStatus === "In Progress" ? "#fff8e6" : "#f5f5f5") : "#fff"
                                                            }}
                                                        >
                                                            <div className="w-100" style={{ height: "10px", backgroundColor: consolidateResponse.assignmentId ? statusColors[consolidatedStatus] : "#fff" }}></div>
                                                            <div className="p-3 flex-grow-1 flex justify-content-between flex-column">
                                                                <h5 className="fw-bold text-wrap">{sectionName}</h5>
                                                                {consolidateResponse.assignmentId && <div className="flex justify-content-between align-items-center">
                                                                    <Button
                                                                        style={{ padding: '5px 0px' }}
                                                                        text
                                                                        label="Consolidate"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            setSelectedConsolidateSection({
                                                                                assignmentId: consolidateResponse.assignmentId,
                                                                                sectionId: consolidateResponse.sectionId
                                                                            });
                                                                            setConsolidateDialogVisible(true);
                                                                        }}
                                                                    />
                                                                    <div>{Object.values(consolidateResponse?.rawResponse).filter(
                                                                        val => val && typeof val.length === 'number' && val.length > 0
                                                                    ).length + '/' + Object.keys(consolidateResponse?.rawResponse || {})?.length}</div>
                                                                </div>}
                                                            </div>
                                                        </div>
                                                    );
                                                });
                                            })()}
                                        </div>
                                    </td>
                                </tr>
                            ) : null;

                            const documentRow = isLastOfGroup ? (
                                <tr key={`documents-${consolidatedRowKey}`}>
                                    <td
                                        className="p-4 align-top bg-white fw-bold"
                                        style={{ width: "300px", minWidth: "250px", position: "sticky", left: 0, zIndex: 2 }}
                                    >
                                        <h3 className="fw-semibold fs-5">Uploaded Documents</h3>
                                        <p className="text-muted">All file attachments</p>
                                    </td>
                                    <td className="p-0">
                                        <div className="p-3" style={{ overflowX: "auto" }}>
                                            <table className="table table-sm table-bordered mb-0" style={{ maxWidth: "960px" }}>
                                                <thead className="bg-light">
                                                    <tr>
                                                        <th>Section</th>
                                                        <th>User</th>
                                                        <th>Label</th>
                                                        <th>File</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {Object.entries(sectionMap).flatMap(([sectionName, responses]) =>
                                                        responses.flatMap((res, idx) => {
                                                            return res.form
                                                                ?.filter(field => field.type === "file" && !!field.value)
                                                                .map((fileField, i) => (
                                                                    <tr key={`${sectionName}-${idx}-${i}`}>
                                                                        <td>{sectionName}</td>
                                                                        <td>{ Number(res.userId)  === 'number' ? getUser(res.userId):res.userId}</td>
                                                                        <td>{fileField.label}</td>
                                                                        <td>
                                                                            <a href={fileField?.value?.includes('api.eisqr') ?  fileField?.value : API.Docs+fileField?.value}  rel="noopener noreferrer">
                                                                                {fileField?.value?.split("/").pop()}
                                                                            </a>
                                                                        </td>
                                                                    </tr>
                                                                )) || [];
                                                        })
                                                    )}
                                                </tbody>
                                            </table>
                                        </div>
                                    </td>
                                </tr>
                            ) : null;

                            return [subHeadingRow, ...rows, consolidatedRow, documentRow];
                        })}
                    </tbody>
                </table>
            </div>

            <Dialog header={selectedItem?.name} visible={visible} style={{ width: "60vw" }} onHide={() => setVisible(false)}>
                <div className="p-3">
                    {selectedItem?.responses ? (
                        <>
                            <h5 className="fw-bold mb-3">All User Responses</h5>
                            {selectedItem.responses.map((userItem, idx) => (
                                userItem.userId !== 'Consolidate' &&
                                <div key={idx} className="mb-4 border rounded p-3 bg-light">
                                    <h6 className="fw-bold mb-3">
                                        Reporter: {getUser(userItem.userId)} | Location: {userItem.location}
                                    </h6>

                                    {userItem.rawResponse &&
                                        Object.entries(userItem.rawResponse).map(([key, value], i) => {
                                            if (key === "status") return null;

                                            const formField = userItem.form?.find(f => f.name === key);
                                            const label = formField?.label || key;
                                            const type = formField?.type;

                                            let displayValue = value;

                                            if (type === "radio-group") {
                                                const option = formField.values?.find(opt => opt.value === value);
                                                displayValue = option?.label || value;
                                            }

                                            if (type === "checkbox-group" && Array.isArray(value)) {
                                                displayValue = value
                                                    .map(v => formField.values?.find(opt => opt.value === v)?.label || v)
                                                    .join(", ");
                                            }

                                            if (type === "file" && typeof value === "string" && value.trim() !== "") {
                                                displayValue = (
                                                    <a href={value?.includes('api.eisqr') ? value : API.Docs+value}  rel="noopener noreferrer">
                                                        {value}
                                                    </a>
                                                );
                                            }

                                            return (
                                                <div key={i} className="mb-3">
                                                    <label className="fw-semibold d-block">{label}</label>
                                                    <div className="text-muted">{displayValue || "-"}</div>
                                                </div>
                                            );
                                        })}
                                </div>
                            ))}
                        </>
                    ) : (
                        <>
                            <h5 className="fw-bold mb-3">Your Response</h5>
                            {selectedItem?.form?.map((field, i) => (
                                <div key={i} className="mt-3">
                                    <label className="fw-bold">{field.label}</label>

                                    {["text", "textarea"].includes(field.type) && (
                                        <div className="mt-2 text-muted border rounded p-2 bg-light">
                                            {formData[field.name] || "-"}
                                        </div>
                                    )}

                                    {field.type === "radio-group" && (
                                        <div className="mt-2 text-muted border rounded p-2 bg-light">
                                            {(() => {
                                                const selectedValue = formData[field.name];
                                                const selectedLabel = field.values.find(v => v.value === selectedValue)?.label;
                                                return selectedLabel || "-";
                                            })()}
                                        </div>
                                    )}

                                    {field.type === "checkbox-group" && (
                                        <div className="mt-2 text-muted border rounded p-2 bg-light">
                                            {(formData[field.name] || [])
                                                .map(val => field.values.find(opt => opt.value === val)?.label || val)
                                                .join(", ") || "-"}
                                        </div>
                                    )}

                                    {field.type === "file" && (
                                        <div className="mt-2">
                                            {formData[field.name] ? (
                                                <a href={formData[field.name]?.includes('api.eisqr') ? formData[field.name] : API.Docs+formData[field.name]   }  rel="noopener noreferrer">
                                                    {formData[field.name]}
                                                </a>
                                            ) : (
                                                <span className="text-muted">-</span>
                                            )}
                                        </div>
                                    )}
                                </div>
                            ))}
                        </>
                    )}
                </div>
            </Dialog>

            {/* Consolidate Dialog */}
            <ConsolidateDialog
                visible={consolidateDialogVisible}
                onHide={() => setConsolidateDialogVisible(false)}
                assignmentId={selectedConsolidateSection.assignmentId}
                sectionId={selectedConsolidateSection.sectionId}
            />
        </div>
    );
};

export default QualitativeResponse;
