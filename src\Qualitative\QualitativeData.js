import React, { useState } from "react";
import { Dialog } from "primereact/dialog";
import { <PERSON><PERSON> } from "primereact/button";
import { InputTextarea } from "primereact/inputtextarea";
import { useSelector } from "react-redux";
import { API } from "../constants/api_url";
import APIServices from "../service/APIService";
import { AttachmentComponent } from "../components/Forms/Attachment";
import { Tooltip } from "primereact/tooltip";
import { FileUpload } from "primereact/fileupload";
import Swal from "sweetalert2";
import moment from "moment";

const statusColors = {
    Completed: "#29C76F",
    "In Progress": "#F5C37B",
    "Not Started": "#CCCED5",
    Blocked: "#fff",
    "Rejected": 'orangered'
};

const QualitativeData = ({ data, refresh }) => {
    const login_data = useSelector((state) => state.user.userdetail);
    const [visible, setVisible] = useState(false);
    const [selectedItem, setSelectedItem] = useState(null);
    const [formData, setFormData] = useState({});

    const handleFileUpload = async (files, fieldName) => {
        const allowedext = ['.jpg', '.JPG', '.jpeg', '.JPEG', '.png', '.PNG', '.bmp', '.pdf', '.PDF', '.xls', '.xlsx', '.doc', '.docx', '.ppt', '.pptx'];

        const validFiles = files.filter(file => {
            const ext = file.name.substring(file.name.lastIndexOf('.'));
            return allowedext.includes(ext);
        });

        if (!validFiles.length) {
            Swal.fire({
                position: "center",
                icon: "warning",
                title: "Invalid file format. Supported: JPEG, PNG, PDF, DOCX etc.",
                showConfirmButton: false,
                timer: 2000,
            });
            return;
        }

        const formDataToUpload = new FormData();
        validFiles.forEach(file => {
            formDataToUpload.append("file", file);
        });

        try {
            const res = await APIServices.post(API.FilesUpload, formDataToUpload, {
                headers: { "Content-Type": "multipart/form-data" }
            });

            const uploadedFile = res.data?.files?.[0]; // assuming your backend returns array of files
            if (uploadedFile?.originalname) {
                // Construct the URL manually
                const url = `${API.baseurl}docs/${uploadedFile.originalname}`;
                setFormData(prev => ({
                    ...prev,
                    [fieldName]: url
                }));
            }

        } catch (err) {
            console.error("File upload failed", err);
            Swal.fire({
                position: "center",
                icon: "error",
                title: "File upload failed",
                showConfirmButton: false,
                timer: 2000,
            });
        }
    };


    const openDialog = (item) => {
        setSelectedItem(item);
        console.log(item)
        const prefilled = {};
        item.form?.forEach((field) => {
            if (["radio-group", "textarea", "text", "file"].includes(field.type)) {
                prefilled[field.name] = field.value || "";
            } else if (field.type === "checkbox-group") {
                prefilled[field.name] = field.values?.filter(v => v.selected)?.map(v => v.value) || [];
            }
        });
        setFormData(prefilled);
        setVisible(true);
    };

    const handleSave = async (status) => {
        console.log(selectedItem)
        if (!selectedItem?.id) return;
        const requiredKeys = selectedItem?.form?.length ? selectedItem?.form?.filter(x => x.required).map(x => x.name) : null
        const hasValidData = requiredKeys ? requiredKeys.every(key => {
            const value = formData[key];
            return (typeof value === 'string' && value.trim() !== '') ||
                (Array.isArray(value) && value.length > 0);
        }) : false

        if (hasValidData || status === "In Progress") {
            const payload = {
                response: {
                    [login_data.id]: { ...formData, ...(status === "Completed" ? {reject:0,type:1,rejected_on: selectedItem?.rejected_on || '',rejected_by: selectedItem?.rejected_by || null ,return_remarks:selectedItem?.return_remarks || []  } : {reject:selectedItem?.rejected , rejected_on: selectedItem?.rejected_on || '',rejected_by: selectedItem?.rejected_by || null, return_remarks:selectedItem?.return_remarks || []  }), status }
                },
                status,
            };
            try {
                const response = await APIServices.patch(`${API.SaveQualitativeResponse(selectedItem.id)}`, payload);
                if (response.status === 204) {
                    setVisible(false);
                    refresh()
                } else {
                    console.error("Save failed", response);
                }
            } catch (err) {
                console.error("Error saving response", err);
            }
        } else {
            Swal.fire({
                position: "center",
                icon: "warning",
                title: "Request to fill all mandatory question to complete submission",
                showConfirmButton: false,
                timer: 2000,
            });
        }

    };

    const frameworkMap = {
        2: "GRI",
        3: "ISSB",
        4: "MCfS",
        5: "SASB",
        6: "TCFD",
        7: "BRSR",
        8: "SGX",
        9: "Boursa Kuwait",
        10: "Bursa Malaysia",
        11: "HKEX",
        12: "NASDAQ",
        13: "CDP",
        14: "EcoVadis",
        15: "CDP",
        16: "EcoVadis",
        17: "MSCI",
        18: "S&P Dow Jones",
        19: "Sustainalitics",
        20: "ISS",
        21: "Carbon Footprint",
        22: "GEF capital",
        24: "BRSR Core",
        25: "CSRD",
        26: "DJSI",
        27: "ESRS",
        28: "IFRS S1",
        29: "IFRS S2",
    };

    return (
        <div className="p-4 border rounded-lg shadow-sm bg-white mb-4">
            <h2 className="fs-4 fw-bold mb-4">{data[0]?.title || "Environment"}</h2>

            <div style={{ overflowX: "auto", maxWidth: "100%" }}>
                <table className="table table-bordered" style={{ minWidth: "1000px", width: "100%" }}>
                    <tbody>
                        {data.map((env, index) => (
                            <tr key={index}>
                                <td
                                    className="p-4 align-top bg-white"
                                    style={{
                                        width: "300px",
                                        minWidth: "250px",
                                        position: "sticky",
                                        left: 0,
                                        zIndex: 2,
                                    }}
                                >
                                    <h3 className="fw-semibold fs-5">{env.subHeading}</h3>
                                    <p className="text-muted"> <strong>Reporting Entity:</strong> {env.location}</p>
                                </td>

                                <td className="p-0">
                                    <div style={{ display: "flex", overflowX: "auto", whiteSpace: "nowrap", maxWidth: "100%" }}>
                                        {env.data.map((item, i) => {
                                            const isBlocked = item.status === "Blocked";
                                            return (
                                                <div
                                                    key={i}
                                                    className={`d-flex flex-column border flex-shrink-0 ${isBlocked ? "text-muted" : "hover-effect"}`}
                                                    style={{
                                                        width: "250px",
                                                        minWidth: "250px",
                                                        cursor: isBlocked ? "not-allowed" : "pointer",
                                                        opacity: isBlocked ? 0.8 : 1,
                                                        pointerEvents: isBlocked ? "none" : "auto",
                                                        borderColor: isBlocked ? "transparent" : undefined,
                                                    }}
                                                    onClick={() => !isBlocked && openDialog(item)}
                                                >
                                                    <div
                                                        className="w-100"
                                                        style={{ height: "10px", backgroundColor: item?.rejected ? statusColors.Rejected : statusColors[item.status] || "#fff" }}
                                                    />
                                                    <div className="p-3 flex-grow-1 flex justify-content-between flex-column">
                                                        <h5 className="fw-bold text-wrap">{item.name}</h5>
                                                        {!isBlocked && <p className={"text-muted mt-2"}>Due date:<br /> <span style={{ fontWeight: item.isOverdue ? 'bold' : 'unset', color: item.isOverdue ? 'rgba(var(--bs-danger-rgb),var(--bs-bg-opacity))!important' : 'unset' }}>{item.dueDate}</span> </p>}
                                                    </div>
                                                    {item.isLocked && (
                                                        <div className="p-2 d-flex justify-content-end">
                                                            <i className="pi pi-lock text-secondary"></i>
                                                        </div>
                                                    )}
                                                </div>
                                            );
                                        })}
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            {/* Dialog */}
            <Dialog header={selectedItem?.name} visible={visible} style={{ width: "50vw" }} onHide={() => setVisible(false)}>
                <div className="p-3">
                    <h6 className="fw-bold">Instructions for entering data:</h6>
                    <p className="text-muted"> Enter your response in the provided text box. Be as clear, specific, and accurate as possible</p>
                    <ul>
                        <li>If supporting documents are required, use the Upload button to attach files.</li>
                        <li>Save your progress regularly by clicking “Save Progress”</li>
                        <li>Responses from multiple entities will be combined for Group-level reporting. When you see the word 'Group level,' tailor your response to the specific reporting entity you are addressing in these qualitative questions.</li>
                    </ul>
                    {selectedItem?.rejected === 1 && <div className="col-12 grid m-0 p-1  fw-7 mb-3 ">
                        <label className="fw-bold  col-6">Remarks <span className="mandatory" >{selectedItem?.rejection_remarks}</span> </label>
                        <label className='col-6'> Rejected on : {selectedItem?.rejected_on ? moment(selectedItem?.rejected_on).format('DD-MMM-YYYY') : ''}  </label>


                    </div>}
                    {Array.isArray(selectedItem?.form) && selectedItem.form.map((field, index) => (


                        <div className="mt-3" key={index}>
                            <Tooltip target={`.tooltip${index}`} position="top" />
                            {field.assignedFramework && (
                                <div className="d-flex flex-wrap gap-2 mt-2">
                                    {Object.entries(field.assignedFramework).map(([frameworkId, labels]) =>
                                        labels.map((labelVal, i) => (
                                            <span
                                                key={`${frameworkId}-${i}`}
                                                className="badge bg-primary text-white px-2 py-1 rounded"
                                                style={{ fontSize: "0.85rem" }}
                                            >
                                                {frameworkMap[frameworkId]} - {labelVal}
                                            </span>
                                        ))
                                    )}
                                </div>
                            )}
                            {(field.type === "textarea" || field.type === "text") && (
                                <>
                                    <label className="fw-bold">

                                        {field.label} {field.required && <span className="mandatory mr-2">*</span>}
                                        {field.description && (
                                            <span>
                                                <i
                                                    className={`material-icons fs-14 tooltip${index}`}
                                                    data-pr-tooltip={field.description}
                                                    style={{ fontSize: "14px", marginLeft: "4px" }}
                                                >help</i>
                                            </span>
                                        )}
                                    </label>
                                    {field.type === "textarea" ? (
                                        <InputTextarea
                                            className="w-100 mt-2"
                                            value={formData[field.name] || ""}
                                            onChange={(e) => setFormData({ ...formData, [field.name]: e.target.value })}
                                        />
                                    ) : (
                                        <input
                                            type="text"
                                            className="form-control mt-2"
                                            value={formData[field.name] || ""}
                                            onChange={(e) => setFormData({ ...formData, [field.name]: e.target.value })}
                                        />
                                    )}
                                </>
                            )}

                            {field.type === "radio-group" && (
                                <>
                                    <label className="fw-bold">
                                        {field.label} {field.required && <span className="mandatory mr-2">*</span>}
                                        {field.description && (
                                            <span>
                                                <i
                                                    className={`material-icons fs-14 tooltip${index}`}
                                                    data-pr-tooltip={field.description}
                                                    style={{ fontSize: "14px", marginLeft: "4px" }}
                                                >help</i>
                                            </span>
                                        )}
                                    </label>
                                    <div className="mt-2">
                                        {field.values.map((option, i) => (
                                            <div className="form-check" key={i}>
                                                <input
                                                    className="form-check-input"
                                                    type="radio"
                                                    name={field.name}
                                                    id={`${field.name}_${option.value}`}
                                                    value={option.value}
                                                    checked={formData[field.name] === option.value}
                                                    onChange={(e) => setFormData({ ...formData, [field.name]: e.target.value })}
                                                />
                                                <label className="form-check-label" htmlFor={`${field.name}_${option.value}`}>
                                                    {option.label}
                                                </label>
                                            </div>
                                        ))}
                                    </div>
                                </>
                            )}

                            {field.type === "checkbox-group" && (
                                <>
                                    <label className="fw-bold">
                                        {field.label} {field.required && <span className="mandatory mr-2">*</span>}
                                        {field.description && (
                                            <span>
                                                <i
                                                    className={`material-icons fs-14 tooltip${index}`}
                                                    data-pr-tooltip={field.description}
                                                    style={{ fontSize: "14px", marginLeft: "4px" }}
                                                >help</i>
                                            </span>
                                        )}
                                    </label>
                                    <div className="mt-2">
                                        {field.values.map((option, i) => (
                                            <div className="form-check" key={i}>
                                                <input
                                                    className="form-check-input"
                                                    type="checkbox"
                                                    id={`${field.name}_${option.value}`}
                                                    checked={formData[field.name]?.includes(option.value)}
                                                    onChange={(e) => {
                                                        const current = formData[field.name] || [];
                                                        const updated = e.target.checked
                                                            ? [...current, option.value]
                                                            : current.filter(v => v !== option.value);
                                                        setFormData({ ...formData, [field.name]: updated });
                                                    }}
                                                />
                                                <label className="form-check-label" htmlFor={`${field.name}_${option.value}`}>
                                                    {option.label}
                                                </label>
                                            </div>
                                        ))}
                                    </div>
                                </>
                            )}

                            {field.type === "file" && (
                                <>
                                    <label className="fw-bold">
                                        {field.label} {field.required && <span className="mandatory mr-2">*</span>}
                                        {field.description && (
                                            <span>
                                                <i
                                                    className={`material-icons fs-14 tooltip${index}`}
                                                    data-pr-tooltip={field.description}
                                                    style={{ fontSize: "14px", marginLeft: "4px" }}
                                                >help</i>
                                            </span>
                                        )}
                                    </label>


                                    <FileUpload
                                        name="file"
                                        customUpload
                                        uploadHandler={(e) => handleFileUpload(e.files, field.name)}
                                        accept=".jpg,.jpeg,.png,.bmp,.pdf,.xls,.xlsx,.doc,.docx,.ppt,.pptx"
                                        maxFileSize={20000000}
                                        chooseLabel="Upload Documents"
                                        uploadLabel="Upload"
                                        cancelLabel="Clear"
                                        mode="basic"
                                        auto
                                        className="mt-2"
                                    />

                                    {formData[field.name] && (
                                        <div className="mt-2">
                                            <a href={formData[field.name]} target="_blank" rel="noopener noreferrer">
                                                {formData[field.name]}
                                            </a>
                                        </div>
                                    )}
                                </>
                            )}
                        </div>
                    ))}
                </div>

                <div className="d-flex justify-content-start gap-2 mt-4">
                    <Button
                        label="Mark as Completed"
                        className="p-button-sm p-button-primary"
                        style={{ backgroundColor: "#004C77", borderColor: "#004C77" }}
                        onClick={() => handleSave("Completed")}
                    />
                    <Button
                        label="Save Progress"
                        className="p-button-sm p-button-outlined p-button-primary"
                        style={{ color: "#004C77", borderColor: "#004C77" }}
                        onClick={() => handleSave("In Progress")}
                    />
                    <Button
                        label="Cancel"
                        className="p-button-sm p-button-text"
                        style={{ color: "#004C77" }}
                        onClick={() => setVisible(false)}
                    />
                </div>
            </Dialog>
        </div>
    );
};

export default QualitativeData;
