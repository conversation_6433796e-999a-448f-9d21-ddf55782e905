import React, { useRef, useEffect, useState } from "react";
import { Card } from "primereact/card";
import { Panel } from "primereact/panel";
import { Tag } from "primereact/tag";
import { Tooltip } from "primereact/tooltip";
import { Accordion, AccordionTab } from "primereact/accordion";
import { OverlayPanel } from "primereact/overlaypanel";

import QualitativeResponse from "./QualitativeResponse";
import APIServices from "../service/APIService";
import { API } from "../constants/api_url";
import { use } from "react";
import { useSelector } from "react-redux";
import { DateTime } from "luxon";
import moment from "moment";
import { merge } from "jquery";
const QualitativeDataResponse = () => {
    const login_data = useSelector((state) => state.user.userdetail)
    const admin_data = useSelector((state) => state.user.admindetail)
    const [rawsitelist, setRawSitelist] = useState([]);


    const [assignedData, setAssignedData] = useState([]);
    const [consolidatordata, setConsolidatorData] = useState([]);
    const [categoryData, setCategoryData] = useState([]);
   
   
     const userList = useSelector(state => state.userlist.userList);
  const tvsExtUserList = useSelector(state => state.userlist.tvsExtUserList);
  const userList_ = [...userList,...tvsExtUserList];

    const getUser = (id) => {
        let user_name = 'Not Found'
        let index = userList_.findIndex(i => i.id === Number(id))
        if (index !== -1) {
            user_name = userList_[index].information.empname
        }
        return user_name
    }
    const getCoverageText = (rowData, rawsitelist) => {
        let text = "";

        if (rowData.level === 0) {
            text = "Corporate";
        } else if (rowData.level === 1) {
            let country_index = rawsitelist.findIndex(
                (i) => i.id === rowData.locationId
            );
            if (country_index !== -1) {
                text = rawsitelist[country_index].name;
            }
        } else if (rowData.level === 2) {
            let city_index = rawsitelist
                .flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )
                .findIndex((i) => {
                    return i.city_id === rowData.locationId;
                });
            if (city_index !== -1) {
                text = rawsitelist.flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )[city_index].city_name;
            }
        } else if (rowData.level === 3) {
            let site_index = rawsitelist
                .flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )
                .findIndex((i) => {
                    return i.site_id === rowData.locationId;
                });
            if (site_index !== -1) {
                text = rawsitelist.flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )[site_index].site_name;
            }
        }
        return text;
    };


    function mergeGroupedSectionData(apiData, localData) {
        const updatedLocalData = {};

        Object.entries(localData).forEach(([categoryName, entries]) => {
            const matchedCategory = apiData.find(cat => cat.name === categoryName);
            if (!matchedCategory) {
                updatedLocalData[categoryName] = entries;
                return;
            }

            updatedLocalData[categoryName] = entries.map(entry => {
                const matchedTopic = matchedCategory.qTopics.find(topic => topic.name === entry.subHeading);
                if (!matchedTopic) return entry;

                const sectionNames = matchedTopic.qSections.map(section => section.name);
                const sectionMap = {};

                // Loop through each user in data
                Object.entries(entry.data).forEach(([userId, userSections]) => {
                    const enrichedSections = {};
                    const enrichedConsolidatedSections = {};

                    sectionNames.forEach(sectionName => {
                        const consolidatedData = consolidatordata.find(x => x.qSectionId === (matchedTopic.qSections.find(s => s.name === sectionName)?.id || null));
                        const assignmentData = assignedData.find(x => x.qSectionId === matchedTopic.qSections.find(s => s.name === sectionName)?.id);

                        enrichedConsolidatedSections[sectionName] = (userId === 'Consolidate' && userSections[sectionName]) || {
                            name: sectionName,
                            status: consolidatedData?.response ? 'Started' : 'Not Started',
                            isLocked: false,
                            sectionId: matchedTopic.qSections.find(s => s.name === sectionName)?.id,
                            assignmentId: consolidatedData?.id,
                            form: assignmentData?.qSection?.srf?.data1
                                ? JSON.parse(assignmentData?.qSection?.srf.data1).map((field) => {
                                    const responseValue = consolidatedData?.response?.[field.name] || null

                                    // Handle radio-group
                                    if (field.type === "radio-group" && Array.isArray(field.values)) {
                                        return {
                                            ...field,
                                            values: field.values.map(option => ({
                                                ...option,
                                                selected: option.value === responseValue
                                            })),
                                            value: responseValue || null
                                        };
                                    }

                                    // Handle checkbox-group
                                    if (field.type === "checkbox-group" && Array.isArray(field.values)) {
                                        const selectedValues = Array.isArray(responseValue) ? responseValue : [];
                                        return {
                                            ...field,
                                            values: field.values.map(option => ({
                                                ...option,
                                                selected: selectedValues.includes(option.value)
                                            })),
                                            value: selectedValues
                                        };
                                    }

                                    // Handle text, textarea, file
                                    if (["text", "textarea", "file"].includes(field.type)) {
                                        return {
                                            ...field,
                                            value: responseValue || ""
                                        };
                                    }

                                    // Default fallback
                                    return {
                                        ...field,
                                        value: responseValue || ""
                                    };
                                })
                                : null
                        };

                        enrichedSections[sectionName] = userSections[sectionName] || {
                            id: matchedTopic.qSections.find(s => s.name === sectionName)?.id || null,
                            name: sectionName,
                            status: 'Blocked',
                            dueDate: null,
                            isLocked: false,
                            form: [],
                        };
                    });
                    console.log(enrichedConsolidatedSections)
                    sectionMap['Consolidate'] = enrichedConsolidatedSections;
                    sectionMap[userId] = enrichedSections;
                });
                return {
                    ...entry,
                    data: sectionMap
                };
            });
        });

        return updatedLocalData;
    }

    function mergeGroupedSectionData_(apiData, localData) {
        const updatedLocalData = {};

        Object.entries(localData).forEach(([categoryName, entries]) => {
            const matchedCategory = apiData.find(cat => cat.name === categoryName);
            if (!matchedCategory) {
                updatedLocalData[categoryName] = entries;
                return;
            }

            updatedLocalData[categoryName] = entries.map(entry => {
                const matchedTopic = matchedCategory.qTopics.find(topic => topic.name === entry.subHeading);
                if (!matchedTopic) return entry;

                const sectionNames = matchedTopic.qSections.map(section => section.name);
                const consolidatedSections = {};

                // Build consolidated section ONCE
                sectionNames.forEach(sectionName => {
                    const section = matchedTopic.qSections.find(s => s.name === sectionName);
                    const sectionId = section?.id || null;

                    const consolidatedData = consolidatordata.find(x => x.qSectionId === sectionId);
                    const assignmentData = assignedData.find(x => x.qSectionId === sectionId);

                    consolidatedSections[sectionName] = {
                        name: sectionName,
                        status: consolidatedData?.response ? 'Started' : 'Not Started',
                        isLocked: false,
                        sectionId,
                        assignmentId: consolidatedData?.id || null,
                        form: assignmentData?.qSection?.srf?.data1
                            ? JSON.parse(assignmentData?.qSection?.srf.data1).map(field => {
                                const responseValue = consolidatedData?.response?.[field.name] || null;

                                if (field.type === "radio-group" && Array.isArray(field.values)) {
                                    return {
                                        ...field,
                                        values: field.values.map(option => ({
                                            ...option,
                                            selected: option.value === responseValue
                                        })),
                                        value: responseValue || null
                                    };
                                }

                                if (field.type === "checkbox-group" && Array.isArray(field.values)) {
                                    const selectedValues = Array.isArray(responseValue) ? responseValue : [];
                                    return {
                                        ...field,
                                        values: field.values.map(option => ({
                                            ...option,
                                            selected: selectedValues.includes(option.value)
                                        })),
                                        value: selectedValues
                                    };
                                }

                                return {
                                    ...field,
                                    value: responseValue || ""
                                };
                            })
                            : null
                    };
                });

                // Initialize full sectionMap with Consolidate section
                const sectionMap = {
                    Consolidate: consolidatedSections
                };

                // Append each userId with their enriched sections
                for (const [userId, userSections] of Object.entries(entry.data)) {
                    const enriched = {};
                    sectionNames.forEach(sectionName => {
                        const section = matchedTopic.qSections.find(s => s.name === sectionName);
                        enriched[sectionName] = userSections?.[sectionName] || {
                            id: section?.id || null,
                            name: sectionName,
                            status: 'Blocked',
                            dueDate: null,
                            isLocked: false,
                            form: []
                        };
                    });
                    sectionMap[userId] = enriched;
                }

                return {
                    ...entry,
                    data: sectionMap
                };
            });
        });

        return updatedLocalData;
    }





    const transformAssignedDataByCategory = (assignedData, consolidatorData) => {
        const grouped = {};

        assignedData.forEach((item, index) => {
            console.log(item)
            const title = item.qCategory.name;
            const subHeading = item.qTopic.name;
            const location = getCoverageText(item, rawsitelist) || 'N/A';

            const sectionName = item?.qSection?.name ?? '';
            const dueDate = moment.utc(item.due_date).local().format("DD/MM/YYYY");
            const key = `${title}__${subHeading}__${location}`;
            if (!grouped[title]) grouped[title] = {};
            if (!grouped[title][key]) {
                grouped[title][key] = {
                    title,
                    subHeading, assignment: item, sectionId: item.qSectionId,
                    location,
                    data: {} // per-user responses
                };
            }

            const allUserResponses = item.response || {};

            Object.entries(allUserResponses).forEach(([userId, userResp]) => {
                if (!grouped[title][key].data[userId]) {
                    grouped[title][key].data[userId] = {};
                }

                grouped[title][key].data[userId][sectionName] = {
                    name: sectionName,
                    status: userResp.status || "Not Started",
                    dueDate,
                    assignment: item,
                    sectionId: item.qSectionId,
                    reporter: getUser(userId),
                    reporterId:userId,
                    isLocked: false,
                    id: item.id, isConsolidator: userId === 'Consolidate',
                    assignmentId: consolidatorData.find(x => x.qSectionId === item.qSectionId)?.id,
                    form: item?.qSection?.srf?.data1
                        ? JSON.parse(item.qSection.srf.data1).map((field) => {
                            const responseValue = userResp?.[field.name];

                            // Handle radio-group
                            if (field.type === "radio-group" && Array.isArray(field.values)) {
                                return {
                                    ...field,
                                    values: field.values.map(option => ({
                                        ...option,
                                        selected: option.value === responseValue
                                    })),
                                    value: responseValue || null
                                };
                            }

                            // Handle checkbox-group
                            if (field.type === "checkbox-group" && Array.isArray(field.values)) {
                                const selectedValues = Array.isArray(responseValue) ? responseValue : [];
                                return {
                                    ...field,
                                    values: field.values.map(option => ({
                                        ...option,
                                        selected: selectedValues.includes(option.value)
                                    })),
                                    value: selectedValues
                                };
                            }

                            // Handle text, textarea, file
                            if (["text", "textarea", "file"].includes(field.type)) {
                                return {
                                    ...field,
                                    value: responseValue || ""
                                };
                            }

                            // Default fallback
                            return {
                                ...field,
                                value: responseValue || ""
                            };
                        })
                        : null
                };
            });
        });

        // Flatten grouped structure to final output
        const finalGroups = {};

        Object.entries(grouped).forEach(([category, group]) => {
            finalGroups[category] = [];

            Object.values(group).forEach(({ title, subHeading, location, data, assignment }) => {
                finalGroups[category].push({ title, subHeading, location, data });
            });
        });

        const merged = mergeGroupedSectionData(categoryData, finalGroups);
        console.log(merged)
        return merged
    };













    const [groupedData, setGroupedData] = useState({});
    const [consolidatedgroupedData, setConsolidatedGroupedData] = useState({});

    useEffect(() => {

        if (consolidatordata.length) {
            const grouped = transformAssignedDataByCategory(assignedData, consolidatordata);
            console.log("Grouped Data", grouped);
            setGroupedData(grouped);
        }
    }, [assignedData, consolidatordata]);


    const getAssignedQualitativeData = async () => {


        const response = await APIServices.post(API.GetAssignedQualitative_Consolidator, {
            userId: login_data.id,
            userProfileId: admin_data.id
        });

        if (response.status === 200 && response.data.status) {
            console.log(response.data)
            setAssignedData(response.data?.reporter?.filter(x => x.qSection && x.qTopic && x.qSection)?.map((i) => ({ ...i, response: { ...i?.response || {}, ...(response.data.consolidation.find(x => x.qCategoryId === i.qCategoryId && x.qTopicId === i.qTopicId && x.qSectionId === i.qSectionId)?.response || {}) } })) || []);
            setConsolidatorData(response.data?.consolidation || []);
        }
    };

    useEffect(async () => {
        let uriString = {
            include: [
                {
                    relation: "locationTwos",
                    scope: { include: [{ relation: "locationThrees" }] },
                },
            ],
        };
        const locationData = await APIServices.get(
            API.LocationOne_UP(admin_data.id) +
            `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
        );
        const categoryData = await APIServices.get(API.getAllQCategory)
        let shapedQlCategory = categoryData.data
            .map((item) => {
                if (item.qTopics) {
                    item.qTopics = item?.qTopics.filter(
                        (locationTwo) =>
                            locationTwo?.qSections &&
                            locationTwo.qSections.length > 0
                    );
                }
                return item;
            })
            .filter((item) => item?.qTopics && item?.qTopics.length > 0);
            
        setCategoryData(shapedQlCategory)
        const shapedSite = locationData?.data?.map((item) => {
            if (item.locationTwos) {
                item.locationTwos = item.locationTwos.filter(
                    (locationTwo) =>
                        locationTwo.locationThrees &&
                        locationTwo.locationThrees.length > 0
                );
            }
            return item;
        }).filter((item) => item.locationTwos && item.locationTwos.length > 0);
        setRawSitelist(shapedSite)
        getAssignedQualitativeData();
    }, []);

    const op1 = useRef(null);
    const op2 = useRef(null);


    return (
        <div className="p-4 max-w-4xl mx-auto">
            <Card className="mb-4">




                <div className="d-flex justify-content-between align-items-center mt-3 p-3">
                    <div className="d-flex gap-3 align-items-center">
                        <span className="d-flex align-items-center gap-2">
                            <span className="rounded-circle bg-secondary" style={{ width: "12px", height: "12px" }}></span> Information required
                        </span>
                        <span className="d-flex align-items-center gap-2">
                            <span className="rounded-circle bg-warning" style={{ width: "12px", height: "12px" }}></span> Draft
                        </span>
                        <span className="d-flex align-items-center gap-2">
                            <span className="rounded-circle bg-success" style={{ width: "12px", height: "12px" }}></span> Data finalised
                        </span>
                        <span className="d-flex align-items-center gap-2">
                            <span className="rounded-circle bg-danger" style={{ width: "12px", height: "12px" }}></span> Overdue submission
                        </span>
                    </div>
                    <div className="d-flex gap-4 align-items-center">
                        <span className="d-flex align-items-center gap-2">
                            <i className="pi pi-lock"></i> Locked for submission
                        </span>
                        <span className="d-flex align-items-center gap-2">
                            <i className="pi pi-lock-open"></i> Unlocked for updates
                        </span>
                    </div>
                </div>

            </Card>
            <div className="w-100">
                {
                    categoryData.map((item) => {
                        return (
                            groupedData[item.name] && <QualitativeResponse refresh={()=>{ getAssignedQualitativeData()}} categoryData={categoryData} data={groupedData[item.name] || []} />
                        )
                    })
                }
                {/* {groupedData["Environment"] && <QualitativeResponse data={groupedData["Environment"] || []} />}
                {groupedData["Social"] && <QualitativeResponse data={groupedData["Social"] || []} />}
                {groupedData["Ethics"] && <QualitativeResponse data={groupedData["Ethics"] || []} />}
                {groupedData["Sustainable Procurement"] && <QualitativeResponse data={groupedData["Sustainable Procurement"] || []} />}
            */}
            </div>

        </div>
    );
};

export default QualitativeDataResponse;